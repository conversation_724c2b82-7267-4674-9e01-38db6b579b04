EntityDefinition(
    name: "Player",
    description: Some("The main character controlled by the player"),
    components: EntityComponents(
        // Core gameplay components matching current hardcoded values
        turn_actor: Some(TurnActorData(
            speed: 100,
            action_queue_size: Some(5),
        )),
        view_shed: Some(ViewShedData(
            radius: 8,
        )),
        tile_sprite: Some(TileSpriteData(
            tile_coords: (10, 18),
            tile_size: Some((12.0, 12.0)),
            tint: None,
        )),

        // Entity type tags
        is_player: Some(true),
        is_ai: Some(false),

        // Spawning properties (not used for player but included for completeness)
        spawn_weight: Some(1.0),
        level_range: None,
    ),
)
