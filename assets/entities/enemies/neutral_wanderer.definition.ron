EntityDefinition(
    name: "Neutral Wanderer",
    description: Some("A creature that minds its own business and wanders aimlessly"),
    components: EntityComponents(
        // Core gameplay components
        turn_actor: Some(TurnActorData(
            speed: 80, // Slow wandering pace
            action_queue_size: Some(1),
        )),
        view_shed: Some(ViewShedData(
            radius: 3, // Limited vision range
        )),
        tile_sprite: Some(TileSpriteData(
            tile_coords: (3, 16), // Different sprite
            tile_size: Some((12.0, 12.0)),
            tint: Some((0.9, 0.9, 1.0, 1.0)), // Slight blue tint for neutral
        )),

        // Entity type tags
        is_player: Some(false),
        is_ai: Some(true),

        // Spawning properties
        spawn_weight: Some(1.5), // Uncommon
        level_range: Some((1, 10)),
    ),
)
