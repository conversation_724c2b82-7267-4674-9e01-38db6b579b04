EntityDefinition(
    name: "Hostile Guard",
    description: Some("An aggressive guard that will chase and attack intruders on sight"),
    components: EntityComponents(
        // Core gameplay components
        turn_actor: Some(TurnActorData(
            speed: 110, // Slightly faster than player
            action_queue_size: Some(3),
        )),
        view_shed: Some(ViewShedData(
            radius: 6, // Good vision range for detection
        )),
        tile_sprite: Some(TileSpriteData(
            tile_coords: (1, 16), // Different sprite from whale
            tile_size: Some((12.0, 12.0)),
            tint: Some((1.0, 0.8, 0.8, 1.0)), // Slight red tint for hostility
        )),

        // Entity type tags
        is_player: Some(false),
        is_ai: Some(true),

        // Spawning properties
        spawn_weight: Some(3.0), // Common enemy
        level_range: Some((1, 5)),
    ),
)
