EntityDefinition(
    name: "Whale",
    description: Some("A large aquatic creature that has somehow found its way into the dungeon"),
    components: EntityComponents(
        // Core gameplay components matching current hardcoded enemy values
        turn_actor: Some(TurnActorData(
            speed: 120,
            action_queue_size: Some(3),
        )),
        view_shed: None, // Enemies don't have view sheds in current implementation
        tile_sprite: Some(TileSpriteData(
            tile_coords: (0, 16),
            tile_size: Some((12.0, 12.0)),
            tint: None,
        )),

        // Entity type tags
        is_player: Some(false),
        is_ai: Some(true),

        // Spawning properties
        spawn_weight: Some(1.0),
        level_range: Some((1, 10)),
    ),
)
