[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = [
    # LLD linker
    # "-Clink-arg=-fuse-ld=lld",

    # Mold linker
    "-Clink-arg=-fuse-ld=/usr/bin/mold",

    # Nightly
    "-Zshare-generics=y",
    "-Zthreads=0",
]

[target.x86_64-apple-darwin]
rustflags = [
    # LLD linker
    #
    # The default ld64 linker is faster, you should continue using it instead.
    #
    # You may need to install it:
    #
    # Brew: `brew install llvm`
    # Manually: <https://lld.llvm.org/MachO/index.html>
    # "-Clink-arg=-fuse-ld=/usr/local/opt/llvm/bin/ld64.lld",

    # Nightly
    "-Zshare-generics=y",
    "-Zthreads=0",
]

[target.aarch64-apple-darwin]
rustflags = [
    # LLD linker
    #
    # The default ld64 linker is faster, you should continue using it instead.
    #
    # You may need to install it:
    #
    # Brew: `brew install llvm`
    # Manually: <https://lld.llvm.org/MachO/index.html>
    # "-Clink-arg=-fuse-ld=/opt/homebrew/opt/llvm/bin/ld64.lld",

    # Nightly
    "-Zshare-generics=y",
    "-Zthreads=0",
]

[target.x86_64-pc-windows-msvc]
# LLD linker
#
# You may need to install it:
#
# ```
# cargo install -f cargo-binutils
# rustup component add llvm-tools
# ```
linker = "rust-lld.exe"
rustdocflags = ["-Clinker=rust-lld.exe"]
rustflags = [
    # Nightly
    # "-Zshare-generics=n", # This needs to be off if you use dynamic linking on Windows.
    # "-Zthreads=0",
]

# Optional: Uncommenting the following improves compile times, but reduces the amount of debug info to 'line number tables only'
# In most cases the gains are negligible, but if you are on macos and have slow compile times you should see significant gains.
# [profile.dev]
# debug = 1

#######################################################
# Enable Cranelift codegen backend for dev profile
# This is controlled dynamically by build.rs and build.sh
# Comment this out for MacOS if you have issues
#######################################################
[unstable]
codegen-backend = true

# Cranelift configuration - uncomment to enable manually
# Note: build.sh script will override this dynamically
[profile.dev]
codegen-backend = "cranelift"

# Keep dependencies using LLVM for better compatibility
[profile.dev.package."*"]
codegen-backend = "llvm"

#######################################################
# End of Cranelift codegen backend for dev profile
#######################################################
