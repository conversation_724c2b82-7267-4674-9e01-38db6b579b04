name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

env:
  # Reduce compile time and cache size.
  RUSTFLAGS: -Dwarnings -Zshare-generics=y -Zthreads=0
  RUSTDOCFLAGS: -Dwarnings -Zshare-generics=y -Zthreads=0
  # Use the same Rust toolchain across jobs so they can share a cache.
  toolchain: nightly-2025-05-01

jobs:
  # Check formatting.
  format:
    name: Format
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.toolchain }}
          components: rustfmt

      - name: Check formatting
        run: cargo fmt --all -- --check

  # Check documentation.
  docs:
    name: Docs
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.toolchain }}

      - name: Restore Rust cache
        id: cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ci
          save-if: false

      - name: Install build dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Check documentation
        run: cargo doc --locked --workspace --profile ci --all-features --document-private-items --no-deps

  # Run Clippy lints.
  clippy-lints:
    name: Clippy lints
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.toolchain }}
          components: clippy

      - name: Restore Rust cache
        id: cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ci
          save-if: ${{ github.ref == 'refs/heads/main' }}

      - name: Install build dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Run Clippy lints
        run: cargo clippy --locked --workspace --all-targets --profile ci --all-features

  # Run Bevy lints.
  bevy-lints:
    name: Bevy lints
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain (plus bevy_lint)
        uses: TheBevyFlock/bevy_cli/bevy_lint@lint-v0.3.0

      - name: Restore Rust cache
        id: cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ci
          save-if: false

      - name: Install build dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Run Bevy lints
        run: bevy_lint --locked --workspace --all-targets --profile ci --all-features

  # Run tests.
  tests:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 40
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up environment
        run: echo "RUSTFLAGS=${RUSTFLAGS:+$RUSTFLAGS }-Zcodegen-backend=cranelift" >> "${GITHUB_ENV}"

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.toolchain }}
          components: rustc-codegen-cranelift-preview

      - name: Restore Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: test
          cache-directories: ${{ env.LD_LIBRARY_PATH }}
          save-if: ${{ github.ref == 'refs/heads/main' }}

      - name: Install build dependencies
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Run tests
        run: cargo test --locked --workspace --all-targets --profile ci --no-fail-fast

  # Check that the web build compiles.
  check-web:
    name: Check web
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ env.toolchain }}
          targets: wasm32-unknown-unknown

      - name: Restore Rust cache
        id: cache
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: web-ci
          save-if: ${{ github.ref == 'refs/heads/main' }}

      - name: Install build dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: sudo apt-get update; sudo apt-get install --no-install-recommends libasound2-dev libudev-dev libwayland-dev

      - name: Check web
        run: cargo check --config 'profile.web.inherits="dev"' --profile ci --no-default-features --features dev --target wasm32-unknown-unknown
